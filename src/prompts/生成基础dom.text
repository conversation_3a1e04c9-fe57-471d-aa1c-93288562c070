你是一名资深的前端工程师，你将要根据我提供的figma信息，生成基础的dom结构，不需要有样式信息，只需要dom即可

你需要了解当前文件结构，然后开始输出一个类名给我，我会输出figma信息给你，然后

要自行设置模板中的designWidth，来匹配rem的换算

当前处理的节点id是：500:4293
当前className是：_500:4293

第一步：先查看跟当前index.html同级的assets目录，有没有名称跟id同名的图片，如果有就需要将这个图片设置为这个节点的背景图片
然后background-image是这个图片，然后background-size: cover,background-repeat: no-repeat,background-position: center;
第二步：根据提供的figma节点信息，完善样式，宽高使用absoluteBoundingBox中的width和height，单位是rem，需要自行换算
第三步：判断是否是文本节点，如果是文本节点需要设置文本内容
第四步：根据我提供的figma信息，在style中新增这个类名的样式信息，基于figma信息实现样式

{
    "id": "500:4293",
    "name": "Today Page",
    "type": "FRAME",
    "scrollBehavior": "SCROLLS",
    "boundVariables": {
        "paddingLeft": {
            "type": "VARIABLE_ALIAS",
            "id": "VariableID:2:48"
        },
        "paddingTop": {
            "type": "VARIABLE_ALIAS",
            "id": "VariableID:19:7"
        },
        "paddingRight": {
            "type": "VARIABLE_ALIAS",
            "id": "VariableID:2:48"
        },
        "paddingBottom": {
            "type": "VARIABLE_ALIAS",
            "id": "VariableID:19:7"
        },
        "fills": [
            {
                "type": "VARIABLE_ALIAS",
                "id": "VariableID:2:8"
            }
        ]
    },
    "explicitVariableModes": {
        "VariableCollectionId:1:35": "2:2"
    },
    "blendMode": "PASS_THROUGH",
    "clipsContent": true,
    "background": [
        {
            "blendMode": "NORMAL",
            "type": "SOLID",
            "color": {
                "r": 0,
                "g": 0,
                "b": 0,
                "a": 1
            },
            "boundVariables": {
                "color": {
                    "type": "VARIABLE_ALIAS",
                    "id": "VariableID:2:8"
                }
            }
        }
    ],
    "fills": [
        {
            "blendMode": "NORMAL",
            "type": "SOLID",
            "color": {
                "r": 0,
                "g": 0,
                "b": 0,
                "a": 1
            },
            "boundVariables": {
                "color": {
                    "type": "VARIABLE_ALIAS",
                    "id": "VariableID:2:8"
                }
            }
        }
    ],
    "strokes": [],
    "strokeWeight": 1,
    "strokeAlign": "INSIDE",
    "backgroundColor": {
        "r": 0,
        "g": 0,
        "b": 0,
        "a": 1
    },
    "layoutGrids": [
        {
            "pattern": "COLUMNS",
            "sectionSize": 75.25,
            "visible": true,
            "color": {
                "r": 1,
                "g": 0,
                "b": 0,
                "a": 0.10000000149011612
            },
            "alignment": "STRETCH",
            "gutterSize": 20,
            "offset": 16,
            "count": 4
        }
    ],
    "layoutMode": "VERTICAL",
    "counterAxisSizingMode": "FIXED",
    "primaryAxisSizingMode": "FIXED",
    "paddingLeft": 16,
    "paddingRight": 16,
    "paddingTop": 32,
    "paddingBottom": 32,
    "layoutWrap": "NO_WRAP",
    "absoluteBoundingBox": {
        "x": 848,
        "y": -5690,
        "width": 393,
        "height": 852
    },
    "absoluteRenderBounds": {
        "x": 848,
        "y": -5690,
        "width": 393,
        "height": 852
    },
    "constraints": {
        "vertical": "TOP",
        "horizontal": "LEFT"
    },
    "layoutSizingHorizontal": "FIXED",
    "layoutSizingVertical": "FIXED",
    "exportSettings": [
        {
            "suffix": "",
            "format": "PNG",
            "constraint": {
                "type": "SCALE",
                "value": 3
            }
        }
    ],
    "effects": [],
    "interactions": []
}

第三步：找到下一个需要完善样式的节点，直接输出给我，我会提供样式信息给你

直接全部完善结束

CSS类名不能以数字开头。我需要使用转义符号来处理以数字开头的类名。让我修正这个问题：